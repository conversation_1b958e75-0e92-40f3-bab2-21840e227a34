namespace MqttBroker.Configuration;

/// <summary>
/// MQTT Broker 配置选项
/// </summary>
public class BrokerConfiguration
{
    /// <summary>
    /// MQTT 服务器端口，默认 1883
    /// </summary>
    public int Port { get; set; } = 1883;

    /// <summary>
    /// 最大客户端连接数，默认 1000
    /// </summary>
    public int MaxConnections { get; set; } = 1000;

    /// <summary>
    /// 是否启用身份验证，默认 false
    /// </summary>
    public bool EnableAuthentication { get; set; } = false;

    /// <summary>
    /// 允许的来源列表，默认允许所有
    /// </summary>
    public string[] AllowedOrigins { get; set; } = ["*"];

    /// <summary>
    /// 是否启用 WebSocket 支持，默认 false
    /// </summary>
    public bool EnableWebSocket { get; set; } = false;

    /// <summary>
    /// WebSocket 端口，默认 8083
    /// </summary>
    public int WebSocketPort { get; set; } = 8083;

    /// <summary>
    /// 是否启用保留消息，默认 true
    /// </summary>
    public bool EnableRetainedMessages { get; set; } = true;

    /// <summary>
    /// 最大保留消息数量，默认 10000
    /// </summary>
    public int MaxRetainedMessages { get; set; } = 10000;
}

/// <summary>
/// 客户端会话信息
/// </summary>
public class ClientSession
{
    /// <summary>
    /// 客户端 ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 连接时间
    /// </summary>
    public DateTime ConnectedAt { get; set; }

    /// <summary>
    /// 最后活动时间
    /// </summary>
    public DateTime LastActivity { get; set; }

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected { get; set; }

    /// <summary>
    /// 客户端 IP 地址
    /// </summary>
    public string? IpAddress { get; set; }

    /// <summary>
    /// 用户名（如果有认证）
    /// </summary>
    public string? Username { get; set; }
}
