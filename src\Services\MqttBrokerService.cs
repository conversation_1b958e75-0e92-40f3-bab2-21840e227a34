using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MQTTnet;
using MQTTnet.Server;
using MqttBroker.Configuration;
using MqttBroker.Handlers;

namespace MqttBroker.Services;

/// <summary>
/// MQTT Broker 核心服务
/// </summary>
public class MqttBrokerService : BackgroundService
{
    private readonly ILogger<MqttBrokerService> _logger;
    private readonly BrokerConfiguration _config;
    private readonly ConnectionHandler _connectionHandler;
    private readonly MessageHandler _messageHandler;
    private MqttServer? _mqttServer;

    public MqttBrokerService(
        ILogger<MqttBrokerService> logger,
        IOptions<BrokerConfiguration> config,
        ConnectionHandler connectionHandler,
        MessageHandler messageHandler)
    {
        _logger = logger;
        _config = config.Value;
        _connectionHandler = connectionHandler;
        _messageHandler = messageHandler;
    }

    /// <summary>
    /// 启动 MQTT Broker 服务
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogInformation("正在启动 MQTT Broker 服务...");

            // 创建 MQTT 服务器选项
            var optionsBuilder = new MqttServerOptionsBuilder()
                .WithDefaultEndpoint()
                .WithDefaultEndpointPort(_config.Port)
                .WithMaxPendingMessagesPerClient(_config.MaxConnections);

            // 注意：在 MQTTnet 4.x 中，WebSocket 支持需要通过 ASP.NET Core 集成来实现
            // 这里我们暂时注释掉 WebSocket 配置，专注于基本的 TCP 功能
            // if (_config.EnableWebSocket)
            // {
            //     // WebSocket 支持需要额外的配置和 ASP.NET Core 集成
            //     _logger.LogInformation("WebSocket 支持已启用，端口: {WebSocketPort}", _config.WebSocketPort);
            // }

            var options = optionsBuilder.Build();

            // 创建 MQTT 服务器
            _mqttServer = new MqttServerFactory().CreateMqttServer(options);

            // 注册事件处理器
            RegisterEventHandlers();

            // 启动服务器
            await _mqttServer.StartAsync();

            _logger.LogInformation("MQTT Broker 已成功启动");
            _logger.LogInformation("监听端口: {Port}", _config.Port);
            _logger.LogInformation("最大连接数: {MaxConnections}", _config.MaxConnections);
            _logger.LogInformation("身份验证: {AuthEnabled}", _config.EnableAuthentication ? "启用" : "禁用");
            _logger.LogInformation("保留消息: {RetainEnabled}", _config.EnableRetainedMessages ? "启用" : "禁用");

            // 等待取消信号
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("MQTT Broker 服务正在停止...");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MQTT Broker 服务启动失败");
            throw;
        }
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("正在停止 MQTT Broker 服务...");

            if (_mqttServer != null)
            {
                await _mqttServer.StopAsync();
                _mqttServer.Dispose();
                _mqttServer = null;
            }

            _logger.LogInformation("MQTT Broker 服务已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止 MQTT Broker 服务时发生错误");
        }

        await base.StopAsync(cancellationToken);
    }

    /// <summary>
    /// 注册事件处理器
    /// </summary>
    private void RegisterEventHandlers()
    {
        if (_mqttServer == null) return;

        // 连接相关事件
        _mqttServer.ValidatingConnectionAsync += _connectionHandler.HandleValidatingConnectionAsync;
        _mqttServer.ClientConnectedAsync += _connectionHandler.HandleClientConnectedAsync;
        _mqttServer.ClientDisconnectedAsync += _connectionHandler.HandleClientDisconnectedAsync;
        _mqttServer.ClientSubscribedTopicAsync += _connectionHandler.HandleClientSubscribedTopicAsync;
        _mqttServer.ClientUnsubscribedTopicAsync += _connectionHandler.HandleClientUnsubscribedTopicAsync;

        // 消息相关事件
        _mqttServer.InterceptingPublishAsync += _messageHandler.HandleInterceptingPublishAsync;
        _mqttServer.InterceptingSubscriptionAsync += _messageHandler.HandleInterceptingSubscriptionAsync;
        _mqttServer.InterceptingUnsubscriptionAsync += _messageHandler.HandleInterceptingUnsubscriptionAsync;

        // 服务器启动完成事件
        _mqttServer.StartedAsync += HandleServerStartedAsync;
        _mqttServer.StoppedAsync += HandleServerStoppedAsync;

        _logger.LogDebug("事件处理器注册完成");
    }

    /// <summary>
    /// 处理服务器启动完成事件
    /// </summary>
    private Task HandleServerStartedAsync(EventArgs eventArgs)
    {
        _logger.LogInformation("MQTT 服务器启动完成");
        return Task.CompletedTask;
    }

    /// <summary>
    /// 处理服务器停止事件
    /// </summary>
    private Task HandleServerStoppedAsync(EventArgs eventArgs)
    {
        _logger.LogInformation("MQTT 服务器已停止");
        return Task.CompletedTask;
    }

    /// <summary>
    /// 获取服务器状态信息
    /// </summary>
    public async Task<object?> GetServerStatusAsync()
    {
        if (_mqttServer == null)
        {
            return new { Status = "Stopped", Message = "服务器未运行" };
        }

        try
        {
            var clients = await _mqttServer.GetClientsAsync();
            // 注意：在 MQTTnet 4.x 中，GetRetainedApplicationMessagesAsync 方法可能不存在
            // 我们暂时移除这个调用，专注于基本功能

            return new
            {
                Status = "Running",
                Port = _config.Port,
                ConnectedClients = clients.Count,
                // RetainedMessages = retainedMessages.Count, // 暂时注释
                MaxConnections = _config.MaxConnections,
                AuthenticationEnabled = _config.EnableAuthentication,
                WebSocketEnabled = _config.EnableWebSocket,
                WebSocketPort = _config.WebSocketPort
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取服务器状态时发生错误");
            return new { Status = "Error", Message = ex.Message };
        }
    }
}
