using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MqttBroker.Configuration;
using MqttBroker.Handlers;
using MqttBroker.Services;

namespace MqttBroker;

/// <summary>
/// MQTT Broker 程序入口
/// </summary>
public class Program
{
    public static async Task Main(string[] args)
    {
        try
        {
            Console.WriteLine("=== MQTT Broker 服务器 ===");
            Console.WriteLine("正在初始化...");

            // 创建主机构建器
            var builder = Host.CreateApplicationBuilder(args);

            // 配置配置文件
            builder.Configuration
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("config/appsettings.json", optional: false, reloadOnChange: true)
                .AddEnvironmentVariables()
                .AddCommandLine(args);

            // 配置日志
            builder.Logging
                .ClearProviders()
                .AddConsole()
                .AddDebug();

            // 注册配置选项
            builder.Services.Configure<BrokerConfiguration>(
                builder.Configuration.GetSection("MqttBroker"));

            // 注册服务
            RegisterServices(builder.Services);

            // 构建主机
            var host = builder.Build();

            // 显示启动信息
            await DisplayStartupInfoAsync(host);

            // 注册优雅关闭处理
            RegisterShutdownHandlers();

            // 运行主机
            await host.RunAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"程序启动失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
            Environment.Exit(1);
        }
    }

    /// <summary>
    /// 注册服务
    /// </summary>
    private static void RegisterServices(IServiceCollection services)
    {
        // 注册单例服务
        services.AddSingleton<BrokerConfiguration>(provider =>
        {
            var configuration = provider.GetRequiredService<IConfiguration>();
            var brokerConfig = new BrokerConfiguration();
            configuration.GetSection("MqttBroker").Bind(brokerConfig);
            return brokerConfig;
        });

        // 注册业务服务
        services.AddSingleton<ClientConnectionService>();
        services.AddSingleton<ConnectionHandler>();
        services.AddSingleton<MessageHandler>();

        // 注册后台服务
        services.AddHostedService<MqttBrokerService>();
    }

    /// <summary>
    /// 显示启动信息
    /// </summary>
    private static async Task DisplayStartupInfoAsync(IHost host)
    {
        var logger = host.Services.GetRequiredService<ILogger<Program>>();
        var config = host.Services.GetRequiredService<BrokerConfiguration>();

        Console.WriteLine();
        Console.WriteLine("=== 配置信息 ===");
        Console.WriteLine($"MQTT 端口: {config.Port}");
        Console.WriteLine($"最大连接数: {config.MaxConnections}");
        Console.WriteLine($"身份验证: {(config.EnableAuthentication ? "启用" : "禁用")}");
        Console.WriteLine($"WebSocket: {(config.EnableWebSocket ? $"启用 (端口: {config.WebSocketPort})" : "禁用")}");
        Console.WriteLine($"保留消息: {(config.EnableRetainedMessages ? "启用" : "禁用")}");
        Console.WriteLine();

        logger.LogInformation("MQTT Broker 配置加载完成");

        await Task.Delay(100); // 给日志一点时间输出
    }

    /// <summary>
    /// 注册优雅关闭处理
    /// </summary>
    private static void RegisterShutdownHandlers()
    {
        Console.CancelKeyPress += (sender, e) =>
        {
            Console.WriteLine();
            Console.WriteLine("接收到停止信号，正在优雅关闭服务器...");
            e.Cancel = true;
        };

        AppDomain.CurrentDomain.ProcessExit += (sender, e) =>
        {
            Console.WriteLine("程序正在退出...");
        };
    }
}

/// <summary>
/// 扩展方法类
/// </summary>
public static class Extensions
{
    /// <summary>
    /// 安全获取配置值
    /// </summary>
    public static T GetValueSafe<T>(this IConfiguration configuration, string key, T defaultValue)
    {
        try
        {
            var value = configuration.GetValue<T>(key);
            return value ?? defaultValue;
        }
        catch
        {
            return defaultValue;
        }
    }
}
