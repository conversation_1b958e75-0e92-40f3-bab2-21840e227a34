using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using MqttBroker.Configuration;

namespace MqttBroker.Services;

/// <summary>
/// 客户端连接管理服务
/// </summary>
public class ClientConnectionService
{
    private readonly ILogger<ClientConnectionService> _logger;
    private readonly BrokerConfiguration _config;
    private readonly ConcurrentDictionary<string, ClientSession> _clientSessions;

    public ClientConnectionService(ILogger<ClientConnectionService> logger, BrokerConfiguration config)
    {
        _logger = logger;
        _config = config;
        _clientSessions = new ConcurrentDictionary<string, ClientSession>();
    }

    /// <summary>
    /// 获取当前连接的客户端数量
    /// </summary>
    public int ConnectedClientsCount => _clientSessions.Count(kvp => kvp.Value.IsConnected);

    /// <summary>
    /// 获取所有客户端会话
    /// </summary>
    public IEnumerable<ClientSession> GetAllSessions() => _clientSessions.Values;

    /// <summary>
    /// 添加或更新客户端会话
    /// </summary>
    public bool AddOrUpdateClientSession(string clientId, string? ipAddress = null, string? username = null)
    {
        try
        {
            // 检查连接数限制
            if (ConnectedClientsCount >= _config.MaxConnections)
            {
                _logger.LogWarning("达到最大连接数限制 {MaxConnections}，拒绝客户端 {ClientId} 连接", 
                    _config.MaxConnections, clientId);
                return false;
            }

            var now = DateTime.UtcNow;
            var session = _clientSessions.AddOrUpdate(clientId, 
                new ClientSession
                {
                    ClientId = clientId,
                    ConnectedAt = now,
                    LastActivity = now,
                    IsConnected = true,
                    IpAddress = ipAddress,
                    Username = username
                },
                (key, existingSession) =>
                {
                    existingSession.ConnectedAt = now;
                    existingSession.LastActivity = now;
                    existingSession.IsConnected = true;
                    existingSession.IpAddress = ipAddress;
                    existingSession.Username = username;
                    return existingSession;
                });

            _logger.LogInformation("客户端 {ClientId} 已连接，IP: {IpAddress}, 用户名: {Username}", 
                clientId, ipAddress ?? "未知", username ?? "匿名");
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加客户端会话时发生错误: {ClientId}", clientId);
            return false;
        }
    }

    /// <summary>
    /// 移除客户端会话
    /// </summary>
    public void RemoveClientSession(string clientId)
    {
        try
        {
            if (_clientSessions.TryGetValue(clientId, out var session))
            {
                session.IsConnected = false;
                _logger.LogInformation("客户端 {ClientId} 已断开连接", clientId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除客户端会话时发生错误: {ClientId}", clientId);
        }
    }

    /// <summary>
    /// 更新客户端最后活动时间
    /// </summary>
    public void UpdateClientActivity(string clientId)
    {
        if (_clientSessions.TryGetValue(clientId, out var session))
        {
            session.LastActivity = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 获取指定客户端会话
    /// </summary>
    public ClientSession? GetClientSession(string clientId)
    {
        _clientSessions.TryGetValue(clientId, out var session);
        return session;
    }

    /// <summary>
    /// 验证客户端是否可以连接
    /// </summary>
    public bool ValidateClientConnection(string clientId, string? username = null, string? password = null)
    {
        // 如果未启用认证，直接允许连接
        if (!_config.EnableAuthentication)
        {
            return true;
        }

        // TODO: 实现具体的认证逻辑
        // 这里可以添加用户名密码验证、证书验证等
        _logger.LogInformation("验证客户端 {ClientId} 连接，用户名: {Username}", clientId, username);
        
        return true; // 暂时允许所有连接
    }
}
