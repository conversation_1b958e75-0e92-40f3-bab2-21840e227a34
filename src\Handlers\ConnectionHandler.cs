using Microsoft.Extensions.Logging;
using MQTTnet;
using MQTTnet.Server;
using MqttBroker.Services;

namespace MqttBroker.Handlers;

/// <summary>
/// MQTT 连接事件处理器
/// </summary>
public class ConnectionHandler
{
    private readonly ILogger<ConnectionHandler> _logger;
    private readonly ClientConnectionService _clientConnectionService;

    public ConnectionHandler(ILogger<ConnectionHandler> logger, ClientConnectionService clientConnectionService)
    {
        _logger = logger;
        _clientConnectionService = clientConnectionService;
    }

    /// <summary>
    /// 处理客户端连接验证
    /// </summary>
    public Task HandleValidatingConnectionAsync(ValidatingConnectionEventArgs eventArgs)
    {
        try
        {
            var clientId = eventArgs.ClientId;
            var username = eventArgs.UserName; // 修正属性名
            var password = eventArgs.Password;
            var endpoint = eventArgs.RemoteEndPoint; // 在 MQTTnet 5.x 中使用 RemoteEndPoint

            _logger.LogInformation("客户端连接验证: ClientId={ClientId}, Username={Username}, Endpoint={Endpoint}",
                clientId, username, endpoint);

            // 验证客户端连接
            var isValid = _clientConnectionService.ValidateClientConnection(clientId, username, password);

            if (!isValid)
            {
                eventArgs.ReasonCode = MQTTnet.Protocol.MqttConnectReasonCode.NotAuthorized;
                _logger.LogWarning("客户端 {ClientId} 连接验证失败", clientId);
            }
            else
            {
                eventArgs.ReasonCode = MQTTnet.Protocol.MqttConnectReasonCode.Success;
                _logger.LogInformation("客户端 {ClientId} 连接验证成功", clientId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理客户端连接验证时发生错误");
            eventArgs.ReasonCode = MQTTnet.Protocol.MqttConnectReasonCode.ServerUnavailable;
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 处理客户端连接事件
    /// </summary>
    public Task HandleClientConnectedAsync(ClientConnectedEventArgs eventArgs)
    {
        try
        {
            var clientId = eventArgs.ClientId;
            var endpoint = eventArgs.RemoteEndPoint; // 在 MQTTnet 5.x 中使用 RemoteEndPoint
            var username = eventArgs.UserName;

            // 提取 IP 地址
            var ipAddress = endpoint?.ToString();

            // 添加客户端会话
            var success = _clientConnectionService.AddOrUpdateClientSession(clientId, ipAddress, username);
            
            if (success)
            {
                _logger.LogInformation("客户端 {ClientId} 已成功连接，当前连接数: {ConnectedCount}", 
                    clientId, _clientConnectionService.ConnectedClientsCount);
            }
            else
            {
                _logger.LogWarning("客户端 {ClientId} 连接失败", clientId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理客户端连接事件时发生错误");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 处理客户端断开连接事件
    /// </summary>
    public Task HandleClientDisconnectedAsync(ClientDisconnectedEventArgs eventArgs)
    {
        try
        {
            var clientId = eventArgs.ClientId;
            var disconnectType = eventArgs.DisconnectType;

            _clientConnectionService.RemoveClientSession(clientId);

            _logger.LogInformation("客户端 {ClientId} 已断开连接，断开类型: {DisconnectType}，当前连接数: {ConnectedCount}", 
                clientId, disconnectType, _clientConnectionService.ConnectedClientsCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理客户端断开连接事件时发生错误");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 处理客户端订阅事件
    /// </summary>
    public Task HandleClientSubscribedTopicAsync(ClientSubscribedTopicEventArgs eventArgs)
    {
        try
        {
            var clientId = eventArgs.ClientId;
            var topicFilter = eventArgs.TopicFilter;

            _clientConnectionService.UpdateClientActivity(clientId);

            _logger.LogInformation("客户端 {ClientId} 订阅主题: {Topic}, QoS: {QoS}", 
                clientId, topicFilter.Topic, topicFilter.QualityOfServiceLevel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理客户端订阅事件时发生错误");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 处理客户端取消订阅事件
    /// </summary>
    public Task HandleClientUnsubscribedTopicAsync(ClientUnsubscribedTopicEventArgs eventArgs)
    {
        try
        {
            var clientId = eventArgs.ClientId;
            var topicFilter = eventArgs.TopicFilter;

            _clientConnectionService.UpdateClientActivity(clientId);

            _logger.LogInformation("客户端 {ClientId} 取消订阅主题: {Topic}", clientId, topicFilter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理客户端取消订阅事件时发生错误");
        }

        return Task.CompletedTask;
    }
}
