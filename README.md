# MQTT Broker 服务器

## 项目概括
本项目旨在开发一个基于 .NET 8 的控制台应用程序，使用 MQTTnet 库创建一个功能完整的 MQTT broker 服务器。该服务器将提供基本的 MQTT 消息代理功能，支持客户端连接、消息发布/订阅等核心特性。

## 技术选型
- 主要编程语言: C# (.NET 8)
- 关键库/框架: 
  - MQTTnet (MQTT 协议实现)
  - Microsoft.Extensions.Hosting (后台服务支持)
  - Microsoft.Extensions.Logging (日志记录)
  - Microsoft.Extensions.Configuration (配置管理)
- 开发工具: Visual Studio 2022 / VS Code
- 版本控制: Git
- 其他工具: NuGet (包管理)

## 项目结构 / 模块划分
- `/src/`: 核心逻辑代码
  - `Program.cs`: 程序入口点和主机配置
  - `/Services/`: 服务层
    - `MqttBrokerService.cs`: MQTT broker 核心服务
    - `ClientConnectionService.cs`: 客户端连接管理服务
  - `/Configuration/`: 配置相关
    - `BrokerConfiguration.cs`: Broker 配置模型
  - `/Handlers/`: 事件处理器
    - `ConnectionHandler.cs`: 连接事件处理
    - `MessageHandler.cs`: 消息处理
- `/config/`: 配置文件目录
  - `appsettings.json`: 应用配置文件
- `MqttBroker.csproj`: 项目文件
- `.gitignore`: Git 忽略配置

## 核心功能 / 模块详解
- `MqttBrokerService`: MQTT broker 核心服务，负责启动和管理 MQTT 服务器实例，处理服务器级别的配置和生命周期管理。
- `ClientConnectionService`: 客户端连接管理，包括连接验证、连接状态跟踪、连接数限制等功能。
- `ConnectionHandler`: 处理客户端连接和断开事件，记录连接日志，管理客户端会话信息。
- `MessageHandler`: 处理消息发布和订阅逻辑，包括主题过滤、消息路由、QoS 处理等。
- `BrokerConfiguration`: 配置管理模块，支持端口设置、连接限制、认证配置等参数的动态配置。

## 数据模型
- ClientSession: { ClientId (string), ConnectedAt (DateTime), LastActivity (DateTime), IsConnected (bool) }
- BrokerSettings: { Port (int), MaxConnections (int), EnableAuthentication (bool), LogLevel (string) }

## 技术实现细节

### 项目初始化和配置
- **项目文件**: 使用 .NET 8 SDK 创建控制台应用，配置了必要的 NuGet 包依赖
- **配置管理**: 通过 `appsettings.json` 进行配置，支持环境变量和命令行参数覆盖
- **依赖注入**: 使用 Microsoft.Extensions.Hosting 提供的 DI 容器管理服务生命周期

### MqttBrokerService (MQTT Broker 核心服务)
- **实现方式**: 继承 `BackgroundService`，作为后台服务运行
- **核心功能**:
  - 使用 MQTTnet 库创建和管理 MQTT 服务器实例
  - 支持标准 MQTT 端口 (1883) 和可选的 WebSocket 端口 (8083)
  - 注册和管理所有 MQTT 事件处理器
- **关键特性**:
  - 优雅启动和关闭机制
  - 实时服务器状态监控
  - 支持最大连接数限制

### ClientConnectionService (客户端连接管理)
- **数据结构**: 使用 `ConcurrentDictionary<string, ClientSession>` 线程安全地管理客户端会话
- **核心功能**:
  - 客户端连接验证和会话管理
  - 连接数限制和活动时间跟踪
  - 支持基本的身份验证框架（可扩展）
- **会话信息**: 包含客户端 ID、连接时间、IP 地址、用户名等

### ConnectionHandler (连接事件处理)
- **事件处理**: 处理客户端连接、断开、订阅、取消订阅等事件
- **日志记录**: 详细记录所有连接活动，便于监控和调试
- **集成**: 与 `ClientConnectionService` 紧密集成，实现连接状态同步

### MessageHandler (消息处理)
- **消息拦截**: 在消息发布前进行验证和过滤
- **主题管理**: 支持标准 MQTT 主题过滤和系统主题处理
- **QoS 支持**: 完整支持 MQTT QoS 0、1、2 级别
- **消息格式**: 自动检测文本和二进制消息格式

### BrokerConfiguration (配置管理)
- **配置模型**: 强类型配置类，包含所有 broker 设置
- **默认值**: 为所有配置项提供合理的默认值
- **验证**: 支持配置验证和热重载（通过 IOptions 模式）

## 开发状态跟踪
| 模块/功能                | 状态     | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|--------------------------|----------|--------|--------------|--------------|-----------|
| 项目初始化和配置         | 已完成   | AI     | 2024-12-19   | 2024-12-19   | [技术实现细节](#项目初始化和配置) |
| MqttBrokerService       | 已完成   | AI     | 2024-12-19   | 2024-12-19   | [技术实现细节](#mqttbrokerservice-mqtt-broker-核心服务) |
| ClientConnectionService | 已完成   | AI     | 2024-12-19   | 2024-12-19   | [技术实现细节](#clientconnectionservice-客户端连接管理) |
| ConnectionHandler       | 已完成   | AI     | 2024-12-19   | 2024-12-19   | [技术实现细节](#connectionhandler-连接事件处理) |
| MessageHandler          | 已完成   | AI     | 2024-12-19   | 2024-12-19   | [技术实现细节](#messagehandler-消息处理) |
| BrokerConfiguration     | 已完成   | AI     | 2024-12-19   | 2024-12-19   | [技术实现细节](#brokerconfiguration-配置管理) |
| 程序入口和主机配置       | 已完成   | AI     | 2024-12-19   | 2024-12-19   | [技术实现细节](#项目初始化和配置) |

## 代码检查与问题记录
[本部分用于记录代码检查结果和开发过程中遇到的问题及其解决方案。]

## 环境设置与运行指南
### 环境要求
- .NET 8 SDK
- Visual Studio 2022 或 VS Code

### 依赖安装
```bash
dotnet add package MQTTnet
dotnet add package Microsoft.Extensions.Hosting
dotnet add package Microsoft.Extensions.Logging
dotnet add package Microsoft.Extensions.Configuration
dotnet add package Microsoft.Extensions.Configuration.Json
```

### 运行命令
```bash
dotnet run
```

### 测试命令
```bash
dotnet test
```
