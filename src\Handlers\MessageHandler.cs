using Microsoft.Extensions.Logging;
using MQTTnet;
using MQTTnet.Server;
using MqttBroker.Services;
using System.Text;

namespace MqttBroker.Handlers;

/// <summary>
/// MQTT 消息处理器
/// </summary>
public class MessageHandler
{
    private readonly ILogger<MessageHandler> _logger;
    private readonly ClientConnectionService _clientConnectionService;

    public MessageHandler(ILogger<MessageHandler> logger, ClientConnectionService clientConnectionService)
    {
        _logger = logger;
        _clientConnectionService = clientConnectionService;
    }

    /// <summary>
    /// 处理消息拦截（发布前验证和消息接收处理）
    /// </summary>
    public Task HandleInterceptingPublishAsync(InterceptingPublishEventArgs eventArgs)
    {
        try
        {
            var clientId = eventArgs.ClientId;
            var topic = eventArgs.ApplicationMessage.Topic;
            var payloadSize = eventArgs.ApplicationMessage.PayloadSegment.Count;
            var qos = eventArgs.ApplicationMessage.QualityOfServiceLevel;
            var retain = eventArgs.ApplicationMessage.Retain;

            // 更新客户端活动时间
            _clientConnectionService.UpdateClientActivity(clientId);

            // 尝试将负载转换为字符串（如果是文本消息）
            string? payloadText = null;
            try
            {
                if (payloadSize > 0)
                {
                    payloadText = Encoding.UTF8.GetString(eventArgs.ApplicationMessage.PayloadSegment.Array!,
                        eventArgs.ApplicationMessage.PayloadSegment.Offset, payloadSize);
                }
            }
            catch
            {
                // 如果不是有效的 UTF-8 文本，保持为 null
            }

            // 记录消息信息（这里同时处理了消息接收和发布前验证）
            _logger.LogInformation("接收到消息 - 客户端: {ClientId}, 主题: {Topic}, QoS: {QoS}, 保留: {Retain}, 负载: {Payload}",
                clientId, topic, qos, retain,
                payloadText ?? $"[二进制数据, {payloadSize} 字节]");

            // 处理特殊系统主题
            if (topic.StartsWith("$SYS/"))
            {
                HandleSystemTopic(topic, payloadText);
            }

            // 可以在这里添加消息过滤逻辑
            // 例如：检查主题权限、消息大小限制、内容过滤等

            // 如果需要阻止消息发布，可以设置：
            // eventArgs.ProcessPublish = false;
            // eventArgs.ReasonCode = MqttPubAckReasonCode.NotAuthorized;

            eventArgs.ProcessPublish = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理消息拦截时发生错误");
            eventArgs.ProcessPublish = false;
            // 注意：在 MQTTnet 4.x 中，InterceptingPublishEventArgs 没有 ReasonCode 属性
            // 如果需要阻止发布，只需设置 ProcessPublish = false
        }

        return Task.CompletedTask;
    }



    /// <summary>
    /// 处理订阅拦截（订阅前验证）
    /// </summary>
    public Task HandleInterceptingSubscriptionAsync(InterceptingSubscriptionEventArgs eventArgs)
    {
        try
        {
            var clientId = eventArgs.ClientId;
            var topicFilter = eventArgs.TopicFilter;

            _logger.LogInformation("客户端 {ClientId} 请求订阅主题过滤器: {TopicFilter}, QoS: {QoS}", 
                clientId, topicFilter.Topic, topicFilter.QualityOfServiceLevel);

            // 可以在这里添加订阅权限验证逻辑
            // 例如：检查客户端是否有权限订阅特定主题

            // 如果需要阻止订阅，可以设置：
            // eventArgs.ProcessSubscription = false;
            // eventArgs.ReasonCode = MqttSubscribeReasonCode.NotAuthorized;

            eventArgs.ProcessSubscription = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理订阅拦截时发生错误");
            eventArgs.ProcessSubscription = false;
            // 注意：在 MQTTnet 4.x 中，InterceptingSubscriptionEventArgs 没有 ReasonCode 属性
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 处理取消订阅拦截
    /// </summary>
    public Task HandleInterceptingUnsubscriptionAsync(InterceptingUnsubscriptionEventArgs eventArgs)
    {
        try
        {
            var clientId = eventArgs.ClientId;
            // 注意：在 MQTTnet 4.x 中，可能需要使用不同的属性名
            // 让我们先简化这个方法，避免使用可能不存在的属性

            _logger.LogInformation("客户端 {ClientId} 请求取消订阅主题", clientId);

            // 通常取消订阅不需要特殊验证，但可以在这里添加逻辑
            eventArgs.ProcessUnsubscription = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理取消订阅拦截时发生错误");
            eventArgs.ProcessUnsubscription = false;
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 处理系统主题消息
    /// </summary>
    private void HandleSystemTopic(string topic, string? payload)
    {
        try
        {
            _logger.LogDebug("处理系统主题: {Topic}, 负载: {Payload}", topic, payload);
            
            // 可以在这里处理系统主题的特殊逻辑
            // 例如：$SYS/broker/clients/connected, $SYS/broker/messages/received 等
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理系统主题时发生错误: {Topic}", topic);
        }
    }
}
