using MQTTnet;
using System.Text;
using System.Buffers;

namespace MqttBroker.Test;

/// <summary>
/// 简单的 MQTT 测试客户端
/// </summary>
public class TestClient
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("=== MQTT 测试客户端 ===");
        
        // 创建 MQTT 客户端 - 在 MQTTnet 5.x 中使用 MqttFactory
        var factory = new MqttFactory();
        var client = factory.CreateMqttClient();

        // 配置连接选项
        var options = new MqttClientOptionsBuilder()
            .WithTcpServer("localhost", 1883)
            .WithClientId("TestClient_" + Guid.NewGuid().ToString("N")[..8])
            .WithCleanSession()
            .Build();

        try
        {
            Console.WriteLine("正在连接到 MQTT Broker...");
            
            // 连接到服务器
            var connectResult = await client.ConnectAsync(options);
            
            if (connectResult.ResultCode == MqttClientConnectResultCode.Success)
            {
                Console.WriteLine("✅ 连接成功！");
                
                // 订阅主题
                var subscribeOptions = new MqttTopicFilterBuilder()
                    .WithTopic("test/topic")
                    .WithAtLeastOnceQoS()
                    .Build();
                
                await client.SubscribeAsync(subscribeOptions);
                Console.WriteLine("📡 已订阅主题: test/topic");
                
                // 设置消息接收处理器
                client.ApplicationMessageReceivedAsync += async e =>
                {
                    // 在 MQTTnet 5.x 中，Payload 是 ReadOnlySequence<byte> 类型
                    var payloadBytes = new byte[e.ApplicationMessage.Payload.Length];
                    e.ApplicationMessage.Payload.CopyTo(payloadBytes);
                    var payload = Encoding.UTF8.GetString(payloadBytes);
                    Console.WriteLine($"📨 收到消息: {e.ApplicationMessage.Topic} -> {payload}");
                };
                
                // 发布测试消息
                for (int i = 1; i <= 5; i++)
                {
                    var message = new MqttApplicationMessageBuilder()
                        .WithTopic("test/topic")
                        .WithPayload($"测试消息 #{i} - {DateTime.Now:HH:mm:ss}")
                        .WithAtLeastOnceQoS()
                        .Build();
                    
                    await client.PublishAsync(message);
                    Console.WriteLine($"📤 发送消息 #{i}");
                    
                    await Task.Delay(1000); // 等待 1 秒
                }
                
                Console.WriteLine("\n按任意键断开连接...");
                Console.ReadKey();
                
                // 断开连接
                await client.DisconnectAsync();
                Console.WriteLine("🔌 已断开连接");
            }
            else
            {
                Console.WriteLine($"❌ 连接失败: {connectResult.ResultCode}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 发生错误: {ex.Message}");
        }
        finally
        {
            client?.Dispose();
        }
    }
}
